from fastapi import FastAPI, File, UploadFile, Request, Response, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.cors import CORSMiddleware
from key import OPENAI_API_KEY
from utils.taiex import taiex1, taiex2, taiex3, taiex4, taiex5
from docx import Document
from io import BytesIO
from utils.us import us1, us2, us3, extract_and_clean_focus_stocks, us4_5
import os
import json
import asyncio
import uuid
from datetime import datetime
from fastapi.staticfiles import StaticFiles
from pathlib import Path
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import threading
import time

app = FastAPI()

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允許所有來源
    allow_credentials=True,
    allow_methods=["*"],  # 允許所有方法
    allow_headers=["*"],  # 允許所有標頭
)

# 確保日誌目錄存在
log_dir = Path("/home/<USER>/ai_env/sam/project/stock_img/log")
log_dir.mkdir(exist_ok=True)

# 任務狀態存儲
task_status = {}
task_results = {}
task_lock = threading.Lock()

# 線程池執行器
executor = ThreadPoolExecutor(max_workers=20)

# 任務清理設置
TASK_CLEANUP_INTERVAL = 3600  # 1小時清理一次
TASK_RETENTION_TIME = 7200    # 保留2小時的任務數據

# 添加 OPTIONS 請求處理
@app.options("/{path:path}")
async def options_handler(request: Request, path: str):
    return Response(status_code=200)

# 掛載 info 目錄為靜態文件
app.mount("/info", StaticFiles(directory="/home/<USER>/ai_env/sam/project/stock_img/info"), name="info")

# 檢查當天的 JSON 文件
@app.get("/check_today_json")
async def check_today_json():
    try:
        # 獲取當天日期 (YYYYMMDD 格式)
        today = datetime.now().strftime("%Y%m%d")
        info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
        today_dir = os.path.join(info_dir, today)
        
        result = {
            "date": today,
            "taiwan_exists": False,
            "us_exists": False,
            "taiwan_data": None,
            "us_data": None
        }
        
        # 檢查目錄是否存在
        if not os.path.exists(today_dir):
            return result
        
        # 檢查台灣股市數據
        taiwan_path = os.path.join(today_dir, "taiwan.json")
        if os.path.exists(taiwan_path):
            result["taiwan_exists"] = True
            with open(taiwan_path, "r", encoding="utf-8") as f:
                result["taiwan_data"] = json.load(f)
        
        # 檢查美國股市數據
        us_path = os.path.join(today_dir, "us.json")
        if os.path.exists(us_path):
            result["us_exists"] = True
            with open(us_path, "r", encoding="utf-8") as f:
                result["us_data"] = json.load(f)
        
        return result
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": f"檢查 JSON 文件失敗: {str(e)}"})

# 執行台灣股市分析的後台任務
def process_taiwan_stock_analysis(task_id: str, txt_lines: List[str], docx_text: str):
    """在後台執行台灣股市分析"""
    try:
        with task_lock:
            task_status[task_id] = {"status": "processing", "progress": 0, "created_at": time.time()}
        
        # 執行分析步驟
        taiex1_result = taiex1(txt_lines, OPENAI_API_KEY)
        with task_lock:
            task_status[task_id]["progress"] = 20
        
        taiex2_result = taiex2(txt_lines, OPENAI_API_KEY)
        with task_lock:
            task_status[task_id]["progress"] = 40
        
        taiex3_result = taiex3(docx_text)
        with task_lock:
            task_status[task_id]["progress"] = 60
        
        to_crawl_taiex5, taiex4_result = taiex4(txt_lines, OPENAI_API_KEY)
        with task_lock:
            task_status[task_id]["progress"] = 80
        
        # 這裡需要在同步函數中調用異步函數
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            taiex5_result, result_tw_urls = loop.run_until_complete(
                taiex5(to_crawl_taiex5, OPENAI_API_KEY)
            )
        finally:
            loop.close()
        
        print(f"result_tw_urls: {result_tw_urls}")
        
        final_result = taiex1_result + taiex2_result + taiex3_result + taiex4_result + taiex5_result
        
        # 保存結果到 info 目錄
        try:
            today = datetime.now().strftime("%Y%m%d")
            info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
            today_dir = os.path.join(info_dir, today)
            os.makedirs(today_dir, exist_ok=True)
            
            json_path = os.path.join(today_dir, "taiwan.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(final_result, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存 taiwan.json 失敗: {str(e)}")
        
        with task_lock:
            task_status[task_id] = {"status": "completed", "progress": 100, "created_at": time.time()}
            task_results[task_id] = final_result
        
    except Exception as e:
        with task_lock:
            task_status[task_id] = {"status": "failed", "error": str(e), "created_at": time.time()}
        print(f"台灣股市分析失敗: {str(e)}")


def extract_first_date(lines):
    import re
    """從 lines 中提取第一個符合 YYYY-MM-DD 或 YYYY-M-DD 格式的日期"""
    # 正則表達式匹配 YYYY-MM-DD 或 YYYY-M-DD 格式
    date_pattern = r'\b(\d{4})-(\d{1,2})-(\d{1,2})\b'
    
    for line in lines:
        match = re.search(date_pattern, line)
        if match:
            return match.group(0)  # 返回完整的日期字符串
    
    return None  # 如果沒有找到日期，返回 None

# 執行美國股市分析的後台任務
def process_us_stock_analysis(task_id: str, txt_lines: List[str], docx_text: str):
    """在後台執行美國股市分析"""
    try:
        with task_lock:
            task_status[task_id] = {"status": "processing", "progress": 0, "created_at": time.time()}
        
        us4_data = extract_and_clean_focus_stocks(txt_lines)

        with task_lock:
            task_status[task_id]["progress"] = 20
        
        us1_result = us1(txt_lines, OPENAI_API_KEY)
        with task_lock:
            task_status[task_id]["progress"] = 40
        
        us2_result = us2(txt_lines, OPENAI_API_KEY)
        with task_lock:
            task_status[task_id]["progress"] = 60
        
        us3_result = us3(docx_text, OPENAI_API_KEY)
        with task_lock:
            task_status[task_id]["progress"] = 80
        
        # 處理異步函數
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        first_date = extract_first_date(txt_lines)
        try:
            us4_5_result = loop.run_until_complete(us4_5(us4_data, OPENAI_API_KEY, first_date))
        finally:
            loop.close()
        
        
        
        us4_result = us4_5_result[0]
        us5_result = us4_5_result[1]
        us5_urls = us4_5_result[2]
        
        final_result = us1_result + us2_result + us3_result + us4_result + us5_result
        
        # 保存結果到 info 目錄
        try:
            today = datetime.now().strftime("%Y%m%d")
            info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
            today_dir = os.path.join(info_dir, today)
            os.makedirs(today_dir, exist_ok=True)
            
            json_path = os.path.join(today_dir, "us.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(final_result, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存 us.json 失敗: {str(e)}")
        
        with task_lock:
            task_status[task_id] = {"status": "completed", "progress": 100, "created_at": time.time()}
            task_results[task_id] = final_result
        
    except Exception as e:
        with task_lock:
            task_status[task_id] = {"status": "failed", "error": str(e), "created_at": time.time()}
        print(f"美國股市分析失敗: {str(e)}")





# 清理過期任務
def cleanup_old_tasks():
    """清理過期的任務數據"""
    current_time = time.time()
    tasks_to_remove = []
    
    with task_lock:
        for task_id, task_info in task_status.items():
            if current_time - task_info.get("created_at", 0) > TASK_RETENTION_TIME:
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            task_status.pop(task_id, None)
            task_results.pop(task_id, None)
    
    if tasks_to_remove:
        print(f"已清理 {len(tasks_to_remove)} 個過期任務")

# 新增：異步任務端點（提交任務並返回task_id）
@app.post("/stock_index_async")
async def upload_files_async(files: list[UploadFile] = File(...)):
    """異步提交台灣股市分析任務"""
    if len(files) != 2:
        return JSONResponse(status_code=400, content={"error": "請同時上傳一個 .txt 和一個 .docx 檔案"})

    txt_lines = None
    docx_text = None

    for file in files:
        filename = file.filename.lower()
        content = await file.read()

        if filename.endswith(".txt"):
            try:
                text = content.decode("utf-8")
                txt_lines = text.strip().splitlines()
                if not txt_lines:
                    return JSONResponse(status_code=400, content={"error": "txt 檔案為空"})
            except UnicodeDecodeError:
                return JSONResponse(status_code=400, content={"error": "無法解碼 txt 檔案"})

        elif filename.endswith(".docx"):
            try:
                doc = Document(BytesIO(content))
                docx_text = '\n'.join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
            except Exception as e:
                return JSONResponse(status_code=500, content={"error": f"docx 檔案處理失敗: {str(e)}"})

        else:
            return JSONResponse(status_code=400, content={"error": f"不支援的檔案格式：{filename}"})

    if txt_lines is None or docx_text is None:
        return JSONResponse(status_code=400, content={"error": "需同時上傳一個 .txt 檔與一個 .docx 檔"})

    # 生成任務ID
    task_id = str(uuid.uuid4())
    
    # 初始化任務狀態
    with task_lock:
        task_status[task_id] = {"status": "queued", "progress": 0, "created_at": time.time()}
    
    # 提交後台任務
    loop = asyncio.get_event_loop()
    loop.run_in_executor(executor, process_taiwan_stock_analysis, task_id, txt_lines, docx_text)
    
    return {
        "task_id": task_id,
        "status": "queued",
        "message": "任務已提交，請使用 task_id 查詢進度"
    }

@app.post("/uss_index_async")
async def upload_uss_files_async(files: list[UploadFile] = File(...)):
    """異步提交美國股市分析任務"""
    if len(files) != 2:
        return JSONResponse(status_code=400, content={"error": "請同時上傳一個 .txt 和一個 .docx 檔案"})

    txt_lines = None
    docx_text = None

    for file in files:
        filename = file.filename.lower()
        content = await file.read()

        if filename.endswith(".txt"):
            try:
                text = content.decode("utf-8")
                txt_lines = text.strip().splitlines()
                if not txt_lines:
                    return JSONResponse(status_code=400, content={"error": "txt 檔案為空"})
            except UnicodeDecodeError:
                return JSONResponse(status_code=400, content={"error": "無法解碼 txt 檔案"})

        elif filename.endswith(".docx"):
            try:
                doc = Document(BytesIO(content))
                docx_text = '\n'.join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
            except Exception as e:
                return JSONResponse(status_code=500, content={"error": f"docx 檔案處理失敗: {str(e)}"})

        else:
            return JSONResponse(status_code=400, content={"error": f"不支援的檔案格式：{filename}"})

    if txt_lines is None or docx_text is None:
        return JSONResponse(status_code=400, content={"error": "需同時上傳一個 .txt 檔與一個 .docx 檔"})

    # 生成任務ID
    task_id = str(uuid.uuid4())
    
    # 初始化任務狀態
    with task_lock:
        task_status[task_id] = {"status": "queued", "progress": 0, "created_at": time.time()}
    
    # 提交後台任務
    loop = asyncio.get_event_loop()
    loop.run_in_executor(executor, process_us_stock_analysis, task_id, txt_lines, docx_text)
    
    return {
        "task_id": task_id,
        "status": "queued",
        "message": "任務已提交，請使用 task_id 查詢進度"
    }

# 保留原有的同步端點（兼容現有前端）
@app.post("/stock_index")
async def upload_files(files: list[UploadFile] = File(...)):
    """同步台灣股市分析（保持兼容性）"""
    if len(files) != 2:
        return JSONResponse(status_code=400, content={"error": "請同時上傳一個 .txt 和一個 .docx 檔案"})

    txt_lines = None
    docx_text = None

    for file in files:
        filename = file.filename.lower()
        content = await file.read()

        if filename.endswith(".txt"):
            try:
                text = content.decode("utf-8")
                txt_lines = text.strip().splitlines()
                if not txt_lines:
                    return JSONResponse(status_code=400, content={"error": "txt 檔案為空"})
            except UnicodeDecodeError:
                return JSONResponse(status_code=400, content={"error": "無法解碼 txt 檔案"})

        elif filename.endswith(".docx"):
            try:
                doc = Document(BytesIO(content))
                docx_text = '\n'.join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
            except Exception as e:
                return JSONResponse(status_code=500, content={"error": f"docx 檔案處理失敗: {str(e)}"})

        else:
            return JSONResponse(status_code=400, content={"error": f"不支援的檔案格式：{filename}"})

    if txt_lines is None or docx_text is None:
        return JSONResponse(status_code=400, content={"error": "需同時上傳一個 .txt 檔與一個 .docx 檔"})

    # 執行分析
    try:
        taiex1_result = taiex1(txt_lines, OPENAI_API_KEY)
        taiex2_result = taiex2(txt_lines, OPENAI_API_KEY)
        taiex3_result = taiex3(docx_text)
        to_crawl_taiex5, taiex4_result = taiex4(txt_lines, OPENAI_API_KEY)
        taiex5_result, result_tw_urls = await taiex5(to_crawl_taiex5, OPENAI_API_KEY)
        print(f"result_tw_urls: {result_tw_urls}")

        final_result = taiex1_result + taiex2_result + taiex3_result + taiex4_result + taiex5_result
        
        # 保存結果到 info 目錄
        try:
            today = datetime.now().strftime("%Y%m%d")
            info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
            today_dir = os.path.join(info_dir, today)
            os.makedirs(today_dir, exist_ok=True)
            
            json_path = os.path.join(today_dir, "taiwan.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(final_result, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存 taiwan.json 失敗: {str(e)}")
        
        return final_result
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": f"分析失敗: {str(e)}"})

@app.post("/uss_index")
async def upload_uss_files(files: list[UploadFile] = File(...)):
    """同步美國股市分析（保持兼容性）"""
    if len(files) != 2:
        return JSONResponse(status_code=400, content={"error": "請同時上傳一個 .txt 和一個 .docx 檔案"})

    txt_lines = None
    docx_text = None

    for file in files:
        filename = file.filename.lower()
        content = await file.read()

        if filename.endswith(".txt"):
            try:
                text = content.decode("utf-8")
                txt_lines = text.strip().splitlines()
                if not txt_lines:
                    return JSONResponse(status_code=400, content={"error": "txt 檔案為空"})
            except UnicodeDecodeError:
                return JSONResponse(status_code=400, content={"error": "無法解碼 txt 檔案"})

        elif filename.endswith(".docx"):
            try:
                doc = Document(BytesIO(content))
                docx_text = '\n'.join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
            except Exception as e:
                return JSONResponse(status_code=500, content={"error": f"docx 檔案處理失敗: {str(e)}"})

        else:
            return JSONResponse(status_code=400, content={"error": f"不支援的檔案格式：{filename}"})

    if txt_lines is None or docx_text is None:
        return JSONResponse(status_code=400, content={"error": "需同時上傳一個 .txt 檔與一個 .docx 檔"})

    try:
        us4_data = extract_and_clean_focus_stocks(txt_lines)
        print(us4_data)
        us1_result = us1(txt_lines, OPENAI_API_KEY)
        us2_result = us2(txt_lines, OPENAI_API_KEY)
        us3_result = us3(docx_text, OPENAI_API_KEY)
        us4_5_result = await us4_5(us4_data, OPENAI_API_KEY)
        us4_result = us4_5_result[0]
        us5_result = us4_5_result[1]
        us5_urls = us4_5_result[2]
        
        final_result = us1_result + us2_result + us3_result + us4_result + us5_result
        
        # 保存結果到 info 目錄
        try:
            today = datetime.now().strftime("%Y%m%d")
            info_dir = "/home/<USER>/ai_env/sam/project/stock_img/info"
            today_dir = os.path.join(info_dir, today)
            os.makedirs(today_dir, exist_ok=True)
            
            json_path = os.path.join(today_dir, "us.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(final_result, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存 us.json 失敗: {str(e)}")
        
        return final_result
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": f"分析失敗: {str(e)}"})

@app.get("/task_status/{task_id}")
async def get_task_status(task_id: str):
    """查詢任務狀態"""
    with task_lock:
        if task_id not in task_status:
            return JSONResponse(status_code=404, content={"error": "任務不存在"})
        
        status = task_status[task_id].copy()
        
        # 如果任務完成，返回結果
        if status.get("status") == "completed" and task_id in task_results:
            status["result"] = task_results[task_id]
        
        return status

@app.get("/task_result/{task_id}")
async def get_task_result(task_id: str):
    """獲取任務結果"""
    with task_lock:
        if task_id not in task_status:
            return JSONResponse(status_code=404, content={"error": "任務不存在"})
        
        status = task_status[task_id]
        
        if status.get("status") == "completed":
            if task_id in task_results:
                return task_results[task_id]
            else:
                return JSONResponse(status_code=404, content={"error": "結果不存在"})
        elif status.get("status") == "failed":
            return JSONResponse(status_code=500, content={"error": status.get("error", "任務執行失敗")})
        else:
            return JSONResponse(status_code=202, content={"message": "任務仍在處理中", "status": status.get("status"), "progress": status.get("progress", 0)})

@app.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """刪除任務（清理資源）"""
    with task_lock:
        if task_id in task_status:
            del task_status[task_id]
        if task_id in task_results:
            del task_results[task_id]
    
    return {"message": "任務已刪除"}

# 獲取所有任務狀態（用於監控）
@app.get("/tasks")
async def get_all_tasks():
    """獲取所有任務狀態"""
    with task_lock:
        return {
            "tasks": task_status.copy(),
            "total_tasks": len(task_status)
        }

@app.post("/test")
async def upload_txt(file: UploadFile = File(...)):
    if not file.filename.endswith(".txt"):
        return JSONResponse(status_code=400, content={"error": "Only .txt files are allowed."})

    content = await file.read()
    text = content.decode("utf-8")

    lines = text.strip().splitlines()
    if not lines:
        return JSONResponse(status_code=400, content={"error": "File is empty."})
   
    result =[[{'type': 'text', 'value': '05/16', 'x': 258, 'y': 58, 'fontSize': 24, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '台股晨訊', 'x': 152, 'y': 110, 'fontSize': 57, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '05/15', 'x': 139, 'y': 165, 'fontSize': 13, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '21730.25', 'x': 153, 'y': 253, 'fontSize': 26, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '52.62', 'x': 176, 'y': 282, 'fontSize': 26, 'fill': '#00A600'}, {'type': 'image', 'value': 'down', 'x': 85, 'y': 270, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '-0.24%', 'x': 168, 'y': 313, 'fontSize': 26, 'fill': '#00A600'}, {'type': 'text', 'value': '230.13', 'x': 170, 'y': 395, 'fontSize': 26, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.34', 'x': 184, 'y': 428, 'fontSize': 26, 'fill': '#00A600'}, {'type': 'image', 'value': 'down', 'x': 85, 'y': 418, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '-0.58%', 'x': 169, 'y': 460, 'fontSize': 26, 'fill': '#00A600'}], [{'type': 'text', 'value': '05/16', 'x': 41, 'y': 55, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '航運業', 'x': 85, 'y': 168, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.06%', 'x': 93, 'y': 203, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'up', 'x': 18, 'y': 190, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '電機機械', 'x': 85, 'y': 298, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '0.82%', 'x': 93, 'y': 335, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'up', 'x': 18, 'y': 323, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '電器電纜', 'x': 85, 'y': 430, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '0.77%', 'x': 93, 'y': 463, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'up', 'x': 18, 'y': 450, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '紡織纖維', 'x': 240, 'y': 168, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.84%', 'x': 247, 'y': 201, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'down', 'x': 175, 'y': 190, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '造紙', 'x': 240, 'y': 298, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.68%', 'x': 247, 'y': 333, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'down', 'x': 175, 'y': 323, 'fontSize': None, 'height': None}, {'type': 'text', 'value': '汽車', 'x': 240, 'y': 430, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '1.48%', 'x': 247, 'y': 463, 'fontSize': 27, 'fill': '#FFFFFF'}, {'type': 'image', 'value': 'down', 'x': 175, 'y': 450, 'fontSize': None, 'height': None}], [{'type':'text','value':'周四大盤終結連續四天收漲，顯示調節賣壓增強，不排除周五盤中仍有拉回的可能，若跌破周四低點21674點，下檔將回測周三帶量長紅低點21552點，若能守住短多仍占優勢，上檔就有望挑戰2萬2甚至半年線以及年線。周四台積電、鴻海連袂收跌拖累大盤，周五須關注台積電、鴻海是否能止穩彈升收紅K。貨櫃航運股長榮、萬海留下長上影線作收，周五走勢宜漲不宜跌。AI概念股偏重個股表現，飛機零組件族群在整理數日之後出現發動跡象，近期走勢可持續加以關注。','x':157,'y':279,'fontSize': 20, 'fill': '#FFFFFF'}], [{'type': 'text', 'value': '台積技術論壇 揭先進製程藍圖', 'x': 172, 'y': 145, 'fontSize': 23, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '工商時報', 'x': 181, 'y': 98, 'fontSize': 21, 'fill': '#FFFFFF'}, {'type': 'text', 'value': 'AI助威 思科調高年度財測', 'x': 172, 'y': 250, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '經濟日報', 'x': 181, 'y': 212, 'fontSize': 21, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '萬海美線訂單艙需求猛增五成', 'x': 172, 'y': 365, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '工商時報', 'x': 181, 'y': 329, 'fontSize': 21, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '皇普喜迎完工交屋潮', 'x': 172, 'y': 482, 'fontSize': 22, 'fill': '#FFFFFF'}, {'type': 'text', 'value': '經濟日報', 'x': 181, 'y': 448, 'fontSize': 21, 'fill': '#FFFFFF'}], []]

    return result

# 獲取連結資料的 API 端點
@app.get("/get_url_json")
async def get_url_json(type: str, date: str = None):
    try:
        # 如果沒有提供日期，使用當天日期
        if not date:
            date = datetime.now().strftime("%Y%m%d")
        
        # 檢查日期格式是否正確
        if not (len(date) == 8 and date.isdigit()):
            return JSONResponse(status_code=400, content={"error": "日期格式不正確，應為 YYYYMMDD"})
        
        # 構建文件路徑
        urls_dir = "/home/<USER>/ai_env/sam/project/stock_img/urls"
        date_dir = os.path.join(urls_dir, date)
        
        # 檢查目錄是否存在
        if not os.path.exists(date_dir):
            return JSONResponse(status_code=404, content={"error": f"找不到日期 {date} 的連結資料"})
        
        # 根據類型讀取不同的文件
        if type == "taiex":
            file_path = os.path.join(date_dir, "taiex.json")
        elif type == "us":
            file_path = os.path.join(date_dir, "us.json")
        else:
            return JSONResponse(status_code=400, content={"error": "類型必須是 'taiex' 或 'us'"})
        
        # 檢查文件是否存在
        if not os.path.exists(file_path):
            return JSONResponse(status_code=404, content={"error": f"找不到 {type} 的連結資料"})
        
        # 讀取文件內容
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        return data
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": f"獲取連結資料失敗: {str(e)}"})

# 日誌記錄 API 端點
@app.post("/log")
@app.options("/log")
async def log_event(log_data: Dict[str, Any] = None):
    if log_data is None:
        return {"success": True}
    try:
        file_path = log_dir / f"log.json"
        log_dir.mkdir(exist_ok=True)
        
        logs = []
        if file_path.exists():
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    logs = json.load(f)
            except json.JSONDecodeError:
                logs = []
        
        logs.append(log_data)
        
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(logs, f, ensure_ascii=False, indent=2)
        
        print(f"日誌已記錄到文件: {file_path}")
        
        return {"success": True}
    except Exception as e:
        print(f"記錄日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"記錄日誌失敗: {str(e)}"})

# 批量日誌記錄 API 端點
@app.post("/log/batch")
@app.options("/log/batch")
async def log_batch(data: Dict[str, List[Dict[str, Any]]] = None):
    if data is None:
        return {"success": True}
    try:
        logs = data.get("logs", [])
        
        if not logs:
            return {"success": True, "count": 0, "message": "沒有日誌需要記錄"}
        
        print(f"收到 {len(logs)} 條日誌進行批量記錄")
        
        log_dir.mkdir(exist_ok=True)
        
        logs_by_date = {}
        for log in logs:
            timestamp = log.get("timestamp", "")
            try:
                date = datetime.fromisoformat(timestamp.replace("Z", "+00:00")).strftime("%Y%m%d")
            except (ValueError, AttributeError):
                date = datetime.now().strftime("%Y%m%d")
            
            if date not in logs_by_date:
                logs_by_date[date] = []
            
            logs_by_date[date].append(log)
        
        for date, date_logs in logs_by_date.items():
            file_path = log_dir / f"log.json"
            
            existing_logs = []
            if file_path.exists():
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        existing_logs = json.load(f)
                except json.JSONDecodeError:
                    existing_logs = []
            
            existing_logs.extend(date_logs)
            
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(existing_logs, f, ensure_ascii=False, indent=2)
            
            print(f"已將 {len(date_logs)} 條日誌寫入文件: {file_path}")
        
        return {"success": True, "count": len(logs)}
    except Exception as e:
        print(f"批量記錄日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"批量記錄日誌失敗: {str(e)}"})

# 獲取日誌 API 端點
@app.get("/logs")
async def get_logs(start_date: Optional[str] = None, end_date: Optional[str] = None,
                   actions: List[str] = Query(default=[]), username: Optional[str] = None):
    try:
        file_path = log_dir / "log.json"
        if not file_path.exists():
            return {"success": True, "logs": []}
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                all_logs = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"讀取日誌文件失敗: {str(e)}")
            return {"success": False, "error": f"讀取日誌文件失敗: {str(e)}"}
        
        filtered_logs = all_logs
        
        if start_date or end_date:
            filtered_logs = []
            for log in all_logs:
                try:
                    timestamp = log.get("timestamp", "")
                    if timestamp:
                        if timestamp.endswith("Z"):
                            timestamp = timestamp[:-1]
                        log_date = datetime.fromisoformat(timestamp)
                        log_date_str = log_date.strftime("%Y%m%d")
                        
                        if start_date and log_date_str < start_date:
                            continue
                        
                        if end_date and log_date_str > end_date:
                            continue
                    
                    filtered_logs.append(log)
                except (ValueError, TypeError) as e:
                    print(f"日期解析錯誤: {str(e)}")
                    if not start_date and not end_date:
                        filtered_logs.append(log)
        
        # 按事件類型過濾（支援多選）
        if actions:
            filtered_logs = [log for log in filtered_logs if log.get("action") in actions]
        
        if username:
            filtered_logs = [log for log in filtered_logs if log.get("username") and username in log.get("username")]
        
        filtered_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        return {"success": True, "logs": filtered_logs}
    except Exception as e:
        print(f"獲取日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"獲取日誌失敗: {str(e)}"})

# 導出日誌 API 端點
@app.get("/export_logs")
async def export_logs(start_date: Optional[str] = None, end_date: Optional[str] = None,
                      actions: List[str] = Query(default=[]), username: Optional[str] = None):
    try:
        file_path = log_dir / "log.json"
        if not file_path.exists():
            return {"success": True, "logs": []}

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                all_logs = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"讀取日誌文件失敗: {str(e)}")
            return {"success": False, "error": f"讀取日誌文件失敗: {str(e)}"}

        # 根據條件過濾日誌
        filtered_logs = all_logs

        # 按日期過濾
        if start_date or end_date:
            filtered_logs = []
            for log in all_logs:
                try:
                    timestamp = log.get("timestamp", "")
                    if timestamp:
                        if timestamp.endswith("Z"):
                            timestamp = timestamp[:-1]
                        log_date = datetime.fromisoformat(timestamp)
                        log_date_str = log_date.strftime("%Y%m%d")

                        if start_date and log_date_str < start_date:
                            continue

                        if end_date and log_date_str > end_date:
                            continue

                    filtered_logs.append(log)
                except (ValueError, TypeError) as e:
                    print(f"日期解析錯誤: {str(e)}")
                    if not start_date and not end_date:
                        filtered_logs.append(log)

        # 按事件類型過濾（支援多選）
        if actions:
            filtered_logs = [log for log in filtered_logs if log.get("action") in actions]

        # 按用戶名過濾
        if username:
            filtered_logs = [log for log in filtered_logs if log.get("username") and username in log.get("username")]

        # 按時間戳排序（最新的在前）
        filtered_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)

        return {"success": True, "logs": filtered_logs}
    except Exception as e:
        print(f"導出日誌失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"導出日誌失敗: {str(e)}"})

# 定期清理任務
@app.on_event("startup")
async def startup_event():
    """應用啟動時執行的任務"""
    import asyncio
    
    async def periodic_cleanup():
        while True:
            await asyncio.sleep(TASK_CLEANUP_INTERVAL)
            cleanup_old_tasks()
    
    # 啟動定期清理任務
    asyncio.create_task(periodic_cleanup())

# 關閉時清理資源
@app.on_event("shutdown")
async def shutdown_event():
    executor.shutdown(wait=True)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=4512)